import { fetchApi, getServerCookies } from "@/api/getapis";
import PatientFile from "@/components/patient-file/PatientFile";
import { PatientFileData } from "@/types/types";
import { API_ROUTES } from "@/utils/ApiRoutes";
import React from "react";

interface PageProps {
  params: Promise<{ slug?: string }>;
  searchParams: Promise<{ id?: string }>;
}

const Page = async ({ searchParams }: PageProps) => {
  const resolvedSearchParams = await searchParams;
  const patientId = resolvedSearchParams.id ?? "";

  let patientData: PatientFileData | null = null;
  if (patientId) {
    // Determine user role to select correct API route
    const role = await getServerCookies("Role");
    console.log("🚀 ~ page ~ role:", role);
    const baseRoute =
      role !== "specialist"
        ? API_ROUTES.PATIENT.GET_PATIENT_BY_ID
        : API_ROUTES.PATIENT.GET_PATIENT_BY_ID_FOR_SPECIALIST;

    const url = `${baseRoute}/${patientId}`;
    const patientsArray = await fetchApi(url);
    console.log("🚀 ~ page ~ patientsArray:", patientsArray);
    patientData = patientsArray as PatientFileData;
  } else {
    console.warn("No patient ID provided in searchParams");
  }
  const Getspecialist = await fetchApi(API_ROUTES.SPECIALIST.GET_SPECIALIST);
0 


  return (
    <>
      <PatientFile
        data={patientData as PatientFileData}
        patientId={patientId}
      />
    </>
  );
};

export default Page;
